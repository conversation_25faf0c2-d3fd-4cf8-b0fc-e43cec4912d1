#!/usr/bin/env python3
"""
Augment 清理工具启动脚本
检查依赖并启动GUI应用
"""

import sys
import subprocess
import importlib.util

def check_pyqt6():
    """检查 PyQt6 是否已安装"""
    try:
        import PyQt6
        return True
    except ImportError:
        return False

def install_pyqt6():
    """安装 PyQt6"""
    print("正在安装 PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6"])
        print("✅ PyQt6 安装成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyQt6 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动 Augment 清理工具...")
    
    # 检查 PyQt6
    if not check_pyqt6():
        print("⚠️ 未检测到 PyQt6，正在尝试安装...")
        if not install_pyqt6():
            print("❌ 无法安装 PyQt6，请手动安装：")
            print("   pip install PyQt6")
            input("按回车键退出...")
            return
    
    # 启动GUI应用
    try:
        from augment_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
