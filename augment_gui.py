#!/usr/bin/env python3
"""
Augment 清理工具 - PyQt6 GUI版本
提供图形界面进行 Augment 数据清理和验证
"""

import sys
import os
import json
import sqlite3
import platform
import subprocess
import tempfile
import shutil
import uuid
import secrets
from pathlib import Path
from datetime import datetime

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QTextEdit, QLabel, QProgressBar, QMessageBox,
    QGroupBox, QSplitter, QStatusBar, QMenuBar, QFileDialog
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt6.QtGui import QFont, QTextCursor, QAction, QIcon

class WorkerThread(QThread):
    """工作线程，处理耗时操作"""
    log_signal = pyqtSignal(str, str)  # message, level
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(bool)  # success
    login_status_signal = pyqtSignal(bool, dict)  # is_logged_in, details

    def __init__(self, operation_type="clean"):
        super().__init__()
        self.operation_type = operation_type
        self.system = platform.system()

    def run(self):
        """执行操作"""
        try:
            if self.operation_type == "clean":
                success = self.perform_cleanup()
            elif self.operation_type == "verify":
                success = self.perform_verification()
            elif self.operation_type == "check_login":
                success = self.perform_login_check()
            else:
                success = False

            self.finished_signal.emit(success)
        except Exception as e:
            self.log_signal.emit(f"操作过程中发生错误: {str(e)}", "ERROR")
            self.finished_signal.emit(False)
    
    def perform_cleanup(self):
        """执行清理操作"""
        self.log_signal.emit("开始 Augment 数据清理...", "INFO")
        self.progress_signal.emit(10)
        
        # 获取路径
        paths = self.get_vscode_paths()
        
        # 停止 VS Code
        self.stop_vscode()
        self.progress_signal.emit(20)
        
        # 清理数据库
        self.clean_database(paths["state_db"])
        self.progress_signal.emit(40)
        
        # 清理存储文件
        self.clean_storage_json(paths["storage_json"])
        self.progress_signal.emit(60)
        
        # 清理全局存储
        self.clean_augment_files(paths["global_storage"], "全局存储")
        self.progress_signal.emit(80)
        
        # 清理临时文件
        self.clean_augment_files(paths["temp_dir"], "临时文件")
        self.progress_signal.emit(90)
        
        # 清理缓存
        self.clean_vscode_general_caches(paths)
        self.progress_signal.emit(100)
        
        self.log_signal.emit("✅ 所有清理操作已完成！请重启 VS Code 并重新登录 Augment。", "SUCCESS")
        return True
    
    def perform_verification(self):
        """执行验证操作"""
        self.log_signal.emit("开始验证清理结果...", "INFO")
        self.progress_signal.emit(20)
        
        paths = self.get_vscode_paths()
        all_clean = True
        
        # 检查数据库
        self.log_signal.emit("检查数据库...", "INFO")
        if not self.check_database(paths["state_db"]):
            all_clean = False
        self.progress_signal.emit(40)
        
        # 检查存储文件
        self.log_signal.emit("检查存储文件...", "INFO")
        if not self.check_storage_json(paths["storage_json"]):
            all_clean = False
        self.progress_signal.emit(70)
        
        # 检查全局存储
        self.log_signal.emit("检查全局存储...", "INFO")
        if not self.check_global_storage(paths["global_storage"]):
            all_clean = False
        self.progress_signal.emit(100)
        
        if all_clean:
            self.log_signal.emit("🎉 验证完成！所有 Augment 相关数据已成功清理。", "SUCCESS")
        else:
            self.log_signal.emit("⚠️ 发现残留数据，建议重新运行清理操作。", "WARNING")
        
        return all_clean

    def perform_login_check(self):
        """检查Augment登录状态"""
        self.log_signal.emit("开始检查 Augment 登录状态...", "INFO")
        self.progress_signal.emit(10)

        paths = self.get_vscode_paths()
        login_details = {
            "database_entries": 0,
            "storage_keys": 0,
            "global_files": 0,
            "is_logged_in": False
        }

        # 检查数据库中的Augment条目
        self.log_signal.emit("检查数据库中的登录信息...", "INFO")
        database_entries = self.count_database_entries(paths["state_db"])
        login_details["database_entries"] = database_entries
        self.progress_signal.emit(30)

        # 检查存储文件中的Augment键
        self.log_signal.emit("检查存储文件中的配置信息...", "INFO")
        storage_keys = self.count_storage_keys(paths["storage_json"])
        login_details["storage_keys"] = storage_keys
        self.progress_signal.emit(60)

        # 检查全局存储中的Augment文件
        self.log_signal.emit("检查全局存储中的相关文件...", "INFO")
        global_files = self.count_global_files(paths["global_storage"])
        login_details["global_files"] = global_files
        self.progress_signal.emit(90)

        # 判断登录状态
        is_logged_in = (database_entries > 0 or storage_keys > 0 or global_files > 0)
        login_details["is_logged_in"] = is_logged_in

        if is_logged_in:
            self.log_signal.emit("🟢 Augment 已登录", "SUCCESS")
            self.log_signal.emit(f"   - 数据库条目: {database_entries}", "INFO")
            self.log_signal.emit(f"   - 存储配置: {storage_keys}", "INFO")
            self.log_signal.emit(f"   - 相关文件: {global_files}", "INFO")
        else:
            self.log_signal.emit("🔴 Augment 未登录或已清理", "WARNING")
            self.log_signal.emit("   - 未找到任何 Augment 相关数据", "INFO")

        self.progress_signal.emit(100)

        # 发送登录状态信号
        self.login_status_signal.emit(is_logged_in, login_details)

        return True

    def count_database_entries(self, db_path):
        """统计数据库中的Augment条目数量"""
        if not db_path.exists():
            return 0

        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except Exception as e:
            self.log_signal.emit(f"检查数据库时出错: {e}", "ERROR")
            return 0

    def count_storage_keys(self, json_path):
        """统计存储文件中的Augment键数量"""
        if not json_path.exists():
            return 0

        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            augment_keys = [key for key in data.keys() if "augment" in key.lower()]
            return len(augment_keys)
        except Exception as e:
            self.log_signal.emit(f"检查存储文件时出错: {e}", "ERROR")
            return 0

    def count_global_files(self, storage_path):
        """统计全局存储中的Augment文件数量"""
        if not storage_path.exists():
            return 0

        try:
            augment_items = list(storage_path.rglob("*augment*"))
            return len(augment_items)
        except Exception as e:
            self.log_signal.emit(f"检查全局存储时出错: {e}", "ERROR")
            return 0

    def get_vscode_paths(self):
        """获取 VS Code 相关路径"""
        paths = {}
        
        if self.system == "Windows":
            appdata = Path(os.environ["APPDATA"])
            base_path = appdata / "Code" / "User"
        elif self.system == "Darwin":  # macOS
            home = Path.home()
            base_path = home / "Library" / "Application Support" / "Code" / "User"
        else:  # Linux
            home = Path.home()
            base_path = home / ".config" / "Code" / "User"
        
        paths["storage_json"] = base_path / "storage.json"
        paths["state_db"] = base_path / "globalStorage" / "state.vscdb"
        paths["global_storage"] = base_path / "globalStorage"
        paths["temp_dir"] = Path(tempfile.gettempdir())
        
        vscode_root_path = base_path.parent 
        paths["vscode_cache"] = vscode_root_path / "Cache"
        paths["vscode_cached_data"] = vscode_root_path / "CachedData"
        paths["vscode_gpu_cache"] = vscode_root_path / "GPUCache"
        
        return paths
    
    def stop_vscode(self):
        """停止 VS Code 进程"""
        self.log_signal.emit("停止 VS Code 进程...", "INFO")
        
        try:
            if self.system == "Windows":
                subprocess.run(["taskkill", "/F", "/IM", "Code.exe"], 
                             capture_output=True, check=False)
            else:
                subprocess.run(["pkill", "-f", "Visual Studio Code"], 
                             capture_output=True, check=False)
            
            self.log_signal.emit("✅ VS Code 进程已停止", "SUCCESS")
        except Exception as e:
            self.log_signal.emit(f"⚠️ 停止进程时出错: {e}", "WARNING")
    
    def clean_database(self, db_path: Path):
        """清理 SQLite 数据库"""
        if not db_path.exists():
            self.log_signal.emit(f"⚠️ 数据库文件不存在: {db_path}", "WARNING")
            return
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
            conn.commit()
            conn.close()
            self.log_signal.emit("✅ 数据库清理完成", "SUCCESS")
        except Exception as e:
            self.log_signal.emit(f"❌ 数据库清理失败: {e}", "ERROR")
    
    def clean_storage_json(self, json_path: Path):
        """清理存储 JSON 文件"""
        if not json_path.exists():
            self.log_signal.emit(f"⚠️ 存储文件不存在: {json_path}", "WARNING")
            return
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 生成新的设备标识符
            new_machine_id = secrets.token_hex(32)
            new_device_id = str(uuid.uuid4())
            
            if "telemetry.machineId" in data:
                data["telemetry.machineId"] = new_machine_id
            if "devDeviceId" in data:
                data["devDeviceId"] = new_device_id
            
            # 移除 augment 相关键
            keys_to_remove = [key for key in data.keys() if "augment" in key.lower()]
            for key in keys_to_remove:
                del data[key]
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.log_signal.emit("✅ 存储文件清理完成", "SUCCESS")
        except Exception as e:
            self.log_signal.emit(f"❌ 存储文件清理失败: {e}", "ERROR")
    
    def clean_augment_files(self, path: Path, name: str):
        """清理 Augment 相关文件"""
        if not path.exists():
            self.log_signal.emit(f"⚠️ {name}路径不存在: {path}", "WARNING")
            return

        items_deleted_count = 0
        try:
            if path.is_dir():
                for item in path.rglob("*augment*"):
                    try:
                        if self.system == "Windows":
                            try:
                                os.chmod(item, 0o777)
                            except:
                                pass
                        
                        if item.is_file():
                            item.unlink()
                            items_deleted_count += 1
                        elif item.is_dir():
                            shutil.rmtree(item)
                            items_deleted_count += 1
                    except Exception as e_item:
                        self.log_signal.emit(f"❌ 删除 {item} 失败: {e_item}", "ERROR")

            if items_deleted_count > 0:
                self.log_signal.emit(f"✅ {name}清理完成，删除 {items_deleted_count} 项", "SUCCESS")
            else:
                self.log_signal.emit(f"ℹ️ {name}中未找到 Augment 相关内容", "INFO")

        except Exception as e:
            self.log_signal.emit(f"❌ {name}清理过程中发生错误: {e}", "ERROR")
    
    def clean_vscode_general_caches(self, paths_config):
        """清理 VS Code 通用缓存目录"""
        cache_paths_map = {
            "VSCode Cache": paths_config.get("vscode_cache"),
            "VSCode CachedData": paths_config.get("vscode_cached_data"),
            "VSCode GPUCache": paths_config.get("vscode_gpu_cache"),
        }

        for cache_name, cache_path in cache_paths_map.items():
            if cache_path and cache_path.exists():
                try:
                    shutil.rmtree(cache_path)
                    self.log_signal.emit(f"✅ {cache_name} 清理成功", "SUCCESS")
                except Exception as e:
                    self.log_signal.emit(f"❌ {cache_name} 清理失败: {e}", "ERROR")
    
    def check_database(self, db_path):
        """检查数据库"""
        if not db_path.exists():
            self.log_signal.emit("✅ 数据库文件不存在", "SUCCESS")
            return True
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%'")
            results = cursor.fetchall()
            conn.close()
            
            if results:
                self.log_signal.emit(f"❌ 数据库中仍有 {len(results)} 个 Augment 条目", "ERROR")
                return False
            else:
                self.log_signal.emit("✅ 数据库检查通过", "SUCCESS")
                return True
        except Exception as e:
            self.log_signal.emit(f"❌ 数据库检查失败: {e}", "ERROR")
            return False
    
    def check_storage_json(self, json_path):
        """检查存储文件"""
        if not json_path.exists():
            self.log_signal.emit("✅ 存储文件不存在", "SUCCESS")
            return True
        
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            augment_keys = [key for key in data.keys() if "augment" in key.lower()]
            
            if augment_keys:
                self.log_signal.emit(f"❌ 存储文件中仍有 {len(augment_keys)} 个 Augment 键", "ERROR")
                return False
            else:
                self.log_signal.emit("✅ 存储文件检查通过", "SUCCESS")
                return True
        except Exception as e:
            self.log_signal.emit(f"❌ 存储文件检查失败: {e}", "ERROR")
            return False
    
    def check_global_storage(self, storage_path):
        """检查全局存储"""
        if not storage_path.exists():
            self.log_signal.emit("✅ 全局存储目录不存在", "SUCCESS")
            return True
        
        augment_items = list(storage_path.rglob("*augment*"))
        
        if augment_items:
            self.log_signal.emit(f"❌ 全局存储中仍有 {len(augment_items)} 个 Augment 项", "ERROR")
            return False
        else:
            self.log_signal.emit("✅ 全局存储检查通过", "SUCCESS")
            return True


class AugmentCleanerGUI(QMainWindow):
    """Augment 清理工具主窗口"""

    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.login_status = None  # 存储登录状态
        self.login_details = {}   # 存储登录详情
        self.init_ui()
        self.setup_connections()

        # 启动时自动检查登录状态
        QTimer.singleShot(1000, self.check_login_status)

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Augment 清理工具 v2.0")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)

        # 创建状态面板
        status_group = self.create_status_panel()
        splitter.addWidget(status_group)

        # 创建控制面板
        control_group = self.create_control_panel()
        splitter.addWidget(control_group)

        # 创建日志面板
        log_group = self.create_log_panel()
        splitter.addWidget(log_group)

        # 设置分割器比例
        splitter.setSizes([150, 200, 400])

        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

        # 创建菜单栏
        self.create_menu_bar()

    def create_status_panel(self):
        """创建状态面板"""
        group = QGroupBox("Augment 登录状态")
        layout = QVBoxLayout(group)

        # 状态显示区域
        status_layout = QHBoxLayout()

        # 状态指示器
        self.status_indicator = QLabel("●")
        self.status_indicator.setStyleSheet("QLabel { color: #95a5a6; font-size: 24px; }")
        status_layout.addWidget(self.status_indicator)

        # 状态文本
        self.status_text = QLabel("检查中...")
        self.status_text.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; }")
        status_layout.addWidget(self.status_text)

        status_layout.addStretch()

        # 刷新按钮
        self.refresh_status_button = QPushButton("🔄 刷新状态")
        self.refresh_status_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        status_layout.addWidget(self.refresh_status_button)

        layout.addLayout(status_layout)

        # 详细信息显示
        self.status_details = QLabel("等待检查...")
        self.status_details.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border-radius: 5px;
                font-size: 11px;
                color: #6c757d;
            }
        """)
        self.status_details.setWordWrap(True)
        layout.addWidget(self.status_details)

        return group

    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("操作控制")
        layout = QVBoxLayout(group)

        # 创建按钮布局
        button_layout = QHBoxLayout()

        # 清理按钮
        self.clean_button = QPushButton("🧹 开始清理")
        self.clean_button.setMinimumHeight(40)
        self.clean_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.clean_button)

        # 验证按钮
        self.verify_button = QPushButton("🔍 验证结果")
        self.verify_button.setMinimumHeight(40)
        self.verify_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.verify_button)

        layout.addLayout(button_layout)

        # 添加检查登录状态按钮
        check_layout = QHBoxLayout()

        self.check_login_button = QPushButton("🔍 检查登录状态")
        self.check_login_button.setMinimumHeight(35)
        self.check_login_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        check_layout.addWidget(self.check_login_button)

        layout.addLayout(check_layout)

        # 添加说明文本
        info_label = QLabel("""
<b>使用说明：</b><br>
• <b>检查登录状态</b>：检查 Augment 当前是否已登录<br>
• <b>开始清理</b>：清除所有 Augment 相关数据和缓存<br>
• <b>验证结果</b>：检查清理是否完全成功<br>
• 清理完成后请重启 VS Code 并重新登录 Augment
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { padding: 10px; background-color: #f8f9fa; border-radius: 5px; }")
        layout.addWidget(info_label)

        return group

    def create_log_panel(self):
        """创建日志面板"""
        group = QGroupBox("操作日志")
        layout = QVBoxLayout(group)

        # 创建日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.log_text)

        # 创建日志控制按钮
        log_button_layout = QHBoxLayout()

        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        log_button_layout.addWidget(self.clear_log_button)

        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        log_button_layout.addWidget(self.save_log_button)

        log_button_layout.addStretch()
        layout.addLayout(log_button_layout)

        return group

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        save_log_action = QAction('保存日志', self)
        save_log_action.triggered.connect(self.save_log)
        file_menu.addAction(save_log_action)

        file_menu.addSeparator()

        exit_action = QAction('退出', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助')

        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_connections(self):
        """设置信号连接"""
        self.clean_button.clicked.connect(self.start_cleanup)
        self.verify_button.clicked.connect(self.start_verification)
        self.check_login_button.clicked.connect(self.check_login_status)
        self.refresh_status_button.clicked.connect(self.check_login_status)
        self.clear_log_button.clicked.connect(self.clear_log)
        self.save_log_button.clicked.connect(self.save_log)

    def start_cleanup(self):
        """开始清理操作"""
        reply = QMessageBox.question(
            self, '确认清理',
            '这将删除所有 Augment 相关数据，包括：\n'
            '• VS Code 中的 Augment 设置\n'
            '• 缓存和临时文件\n'
            '• 设备标识信息\n\n'
            '确定要继续吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.execute_operation("clean")

    def start_verification(self):
        """开始验证操作"""
        self.execute_operation("verify")

    def check_login_status(self):
        """检查登录状态"""
        self.execute_operation("check_login")

    def execute_operation(self, operation_type):
        """执行操作"""
        if self.worker_thread and self.worker_thread.isRunning():
            QMessageBox.warning(self, "操作进行中", "请等待当前操作完成")
            return

        # 禁用按钮
        self.clean_button.setEnabled(False)
        self.verify_button.setEnabled(False)
        self.check_login_button.setEnabled(False)
        self.refresh_status_button.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 更新状态
        status_map = {
            "clean": "正在清理...",
            "verify": "正在验证...",
            "check_login": "正在检查登录状态..."
        }
        status_text = status_map.get(operation_type, "正在处理...")
        self.status_bar.showMessage(status_text)

        # 创建并启动工作线程
        self.worker_thread = WorkerThread(operation_type)
        self.worker_thread.log_signal.connect(self.append_log)
        self.worker_thread.progress_signal.connect(self.progress_bar.setValue)
        self.worker_thread.finished_signal.connect(self.operation_finished)
        self.worker_thread.login_status_signal.connect(self.update_login_status)
        self.worker_thread.start()

    def append_log(self, message, level):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据级别设置颜色
        color_map = {
            "INFO": "#3498db",      # 蓝色
            "SUCCESS": "#27ae60",   # 绿色
            "WARNING": "#f39c12",   # 橙色
            "ERROR": "#e74c3c"      # 红色
        }

        color = color_map.get(level, "#ecf0f1")

        # 格式化消息
        formatted_message = f'<span style="color: #95a5a6;">[{timestamp}]</span> <span style="color: {color};">{message}</span>'

        # 添加到日志
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def operation_finished(self, success):
        """操作完成"""
        # 启用按钮
        self.clean_button.setEnabled(True)
        self.verify_button.setEnabled(True)

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 更新状态
        if success:
            self.status_bar.showMessage("操作完成", 3000)
        else:
            self.status_bar.showMessage("操作失败", 3000)

        # 清理线程
        if self.worker_thread:
            self.worker_thread.deleteLater()
            self.worker_thread = None

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.append_log("日志已清空", "INFO")

    def save_log(self):
        """保存日志"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存日志",
            f"augment_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    # 获取纯文本内容
                    plain_text = self.log_text.toPlainText()
                    f.write(plain_text)

                QMessageBox.information(self, "保存成功", f"日志已保存到：\n{filename}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存日志时出错：\n{str(e)}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于 Augment 清理工具",
            "<h3>Augment 清理工具 v2.0</h3>" +
            "<p>用于清理 VS Code 中的 Augment 相关数据</p>" +
            "<p><b>功能：</b></p>" +
            "<ul>" +
            "<li>清理 Augment 设置和缓存</li>" +
            "<li>重置设备标识信息</li>" +
            "<li>验证清理结果</li>" +
            "</ul>" +
            "<p><b>使用方法：</b></p>" +
            "<ol>" +
            "<li>点击'开始清理'清除所有数据</li>" +
            "<li>点击'验证结果'检查清理状态</li>" +
            "<li>重启 VS Code 并重新登录 Augment</li>" +
            "</ol>"
        )

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.worker_thread and self.worker_thread.isRunning():
            reply = QMessageBox.question(
                self, '确认退出',
                '操作正在进行中，确定要退出吗？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                if self.worker_thread:
                    self.worker_thread.terminate()
                    self.worker_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("Augment 清理工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Augment Cleaner")

    # 创建并显示主窗口
    window = AugmentCleanerGUI()
    window.show()

    # 添加欢迎消息
    window.append_log("欢迎使用 Augment 清理工具 v2.0", "INFO")
    window.append_log("请选择要执行的操作", "INFO")

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
