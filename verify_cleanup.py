#!/usr/bin/env python3
"""
验证 Augment 清理是否完成
"""

import os
import sqlite3
import json
import platform
from pathlib import Path

def check_vscode_paths():
    """检查 VS Code 相关路径中是否还有 Augment 残留"""
    system = platform.system()
    
    if system == "Windows":
        appdata = Path(os.environ["APPDATA"])
        base_path = appdata / "Code" / "User"
    elif system == "Darwin":  # macOS
        home = Path.home()
        base_path = home / "Library" / "Application Support" / "Code" / "User"
    else:  # Linux
        home = Path.home()
        base_path = home / ".config" / "Code" / "User"
    
    paths = {
        "storage_json": base_path / "storage.json",
        "state_db": base_path / "globalStorage" / "state.vscdb",
        "global_storage": base_path / "globalStorage",
    }
    
    return paths

def check_database(db_path):
    """检查数据库中是否还有 Augment 相关条目"""
    if not db_path.exists():
        print(f"✅ 数据库文件不存在: {db_path}")
        return True
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        cursor.execute("SELECT key FROM ItemTable WHERE key LIKE '%augment%'")
        results = cursor.fetchall()
        conn.close()
        
        if results:
            print(f"❌ 数据库中仍有 {len(results)} 个 Augment 相关条目:")
            for row in results:
                print(f"   - {row[0]}")
            return False
        else:
            print("✅ 数据库中没有 Augment 相关条目")
            return True
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        return False

def check_storage_json(json_path):
    """检查存储 JSON 文件中是否还有 Augment 相关条目"""
    if not json_path.exists():
        print(f"✅ 存储文件不存在: {json_path}")
        return True
    
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        augment_keys = [key for key in data.keys() if "augment" in key.lower()]
        
        if augment_keys:
            print(f"❌ 存储文件中仍有 {len(augment_keys)} 个 Augment 相关键:")
            for key in augment_keys:
                print(f"   - {key}")
            return False
        else:
            print("✅ 存储文件中没有 Augment 相关键")
            return True
    except Exception as e:
        print(f"❌ 检查存储文件时出错: {e}")
        return False

def check_global_storage(storage_path):
    """检查全局存储目录中是否还有 Augment 相关文件"""
    if not storage_path.exists():
        print(f"✅ 全局存储目录不存在: {storage_path}")
        return True
    
    augment_items = list(storage_path.rglob("*augment*"))
    
    if augment_items:
        print(f"❌ 全局存储中仍有 {len(augment_items)} 个 Augment 相关项:")
        for item in augment_items:
            print(f"   - {item}")
        return False
    else:
        print("✅ 全局存储中没有 Augment 相关项")
        return True

def main():
    print("🔍 验证 Augment 清理结果...")
    print("=" * 50)
    
    paths = check_vscode_paths()
    all_clean = True
    
    # 检查数据库
    print("\n📊 检查数据库...")
    if not check_database(paths["state_db"]):
        all_clean = False
    
    # 检查存储文件
    print("\n📄 检查存储文件...")
    if not check_storage_json(paths["storage_json"]):
        all_clean = False
    
    # 检查全局存储
    print("\n📁 检查全局存储...")
    if not check_global_storage(paths["global_storage"]):
        all_clean = False
    
    print("\n" + "=" * 50)
    if all_clean:
        print("🎉 清理验证完成！所有 Augment 相关数据已成功清理。")
        print("💡 现在可以重启 VS Code 并重新登录 Augment。")
    else:
        print("⚠️  发现残留的 Augment 数据，可能需要手动清理或重新运行清理脚本。")
    
    return all_clean

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
