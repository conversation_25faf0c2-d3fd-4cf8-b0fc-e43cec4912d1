# Augment 清理工具 v2.0

一个用于清理 VS Code 中 Augment 相关数据的图形界面工具。

## 功能特性

- 🧹 **完整清理**：清除所有 Augment 相关设置、缓存和临时文件
- 🔍 **验证功能**：检查清理是否完全成功
- 📝 **实时日志**：彩色日志输出，实时显示操作进度
- 💾 **日志保存**：支持将日志保存到文件
- 🎯 **用户友好**：直观的图形界面，操作简单
- ⚡ **多线程**：后台处理，界面不会冻结

## 安装要求

- Python 3.7+
- PyQt6（会自动安装）

## 使用方法

### 方法1：使用启动脚本（推荐）
```bash
python run_gui.py
```

### 方法2：直接运行GUI
```bash
# 首先安装依赖
pip install PyQt6

# 运行GUI
python augment_gui.py
```

## 界面说明

### 主要功能按钮

1. **🧹 开始清理**
   - 清除所有 Augment 相关数据
   - 包括设置、缓存、临时文件
   - 重置设备标识信息

2. **🔍 验证结果**
   - 检查清理是否完全成功
   - 显示残留数据（如果有）

### 日志面板

- **实时显示**：操作过程中的所有信息
- **颜色区分**：
  - 🔵 蓝色：一般信息
  - 🟢 绿色：成功操作
  - 🟠 橙色：警告信息
  - 🔴 红色：错误信息

### 日志控制

- **清空日志**：清除当前显示的日志
- **保存日志**：将日志保存到文本文件

## 清理内容

工具会清理以下内容：

### VS Code 数据库
- `state.vscdb` 中的所有 Augment 相关条目

### 存储文件
- `storage.json` 中的 Augment 设置
- 重新生成设备标识符

### 全局存储
- 全局存储目录中的 Augment 文件和文件夹

### 临时文件
- 系统临时目录中的 Augment 相关文件

### 缓存目录
- VS Code Cache
- VS Code CachedData  
- VS Code GPUCache

## 使用流程

1. **启动工具**
   ```bash
   python run_gui.py
   ```

2. **执行清理**
   - 点击"开始清理"按钮
   - 确认清理操作
   - 等待清理完成

3. **验证结果**（可选）
   - 点击"验证结果"按钮
   - 查看验证报告

4. **重启 VS Code**
   - 完全关闭 VS Code
   - 重新启动 VS Code
   - 重新登录 Augment

## 注意事项

⚠️ **重要提醒**：
- 清理操作会删除所有 Augment 相关数据
- 清理前请确保已保存重要工作
- 清理后需要重新登录 Augment
- 建议在清理前关闭 VS Code

## 故障排除

### 权限问题
如果遇到权限错误：
- 以管理员身份运行工具
- 或手动删除提示的文件

### PyQt6 安装失败
```bash
# 手动安装 PyQt6
pip install PyQt6

# 如果仍然失败，尝试
pip install --upgrade pip
pip install PyQt6
```

### VS Code 进程无法停止
- 手动关闭所有 VS Code 窗口
- 使用任务管理器结束 Code.exe 进程

## 文件说明

- `augment_gui.py` - 主GUI应用程序
- `run_gui.py` - 启动脚本（自动检查依赖）
- `augment_clean.py` - 命令行版本清理工具
- `verify_cleanup.py` - 命令行版本验证工具

## 版本历史

### v2.0
- 添加图形界面
- 实时日志显示
- 多线程处理
- 验证功能集成

### v1.0
- 基础命令行清理功能

## 支持

如果遇到问题，请检查：
1. Python 版本是否为 3.7+
2. PyQt6 是否正确安装
3. 是否有足够的系统权限

---

**免责声明**：此工具会删除 Augment 相关数据，使用前请确保了解操作后果。
